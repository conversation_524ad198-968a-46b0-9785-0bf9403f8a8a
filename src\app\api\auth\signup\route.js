import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import bcrypt from 'bcryptjs';
import { sendWelcomeEmail } from '@/lib/email';

export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();
    const { 
      firstname, 
      surname, 
      email, 
      password, 
      confirmPassword,
      phone,
      acceptTerms 
    } = body;

    // Validation
    const errors = {};

    if (!firstname || firstname.trim().length < 2) {
      errors.firstname = 'First name must be at least 2 characters';
    }

    if (!surname || surname.trim().length < 2) {
      errors.surname = 'Surname must be at least 2 characters';
    }

    if (!email || !/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!password || password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    if (!acceptTerms) {
      errors.acceptTerms = 'You must accept the terms and conditions';
    }

    if (Object.keys(errors).length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'Please fix the following errors',
          errors,
        },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json(
        {
          success: false,
          error: 'User Exists',
          message: 'An account with this email already exists',
        },
        { status: 409 }
      );
    }

    // Auto-assign admin role to specific emails
    const adminEmails = [
      process.env.ADMIN_EMAIL,
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ].filter(Boolean);

    const role = adminEmails.includes(email.toLowerCase()) ? 'admin' : 'user';

    // Create user
    const user = new User({
      firstname: firstname.trim(),
      surname: surname.trim(),
      name: `${firstname.trim()} ${surname.trim()}`,
      email: email.toLowerCase(),
      password, // Will be hashed by pre-save middleware
      phone: phone?.trim() || undefined,
      role,
      provider: 'credentials',
      isActive: true,
      loginCount: 0,
    });

    await user.save();

    // Send welcome email (don't fail registration if email fails)
    try {
      await sendWelcomeEmail(user);
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
      // Continue with registration success even if email fails
    }

    // Remove password from response
    const userResponse = {
      id: user._id,
      firstname: user.firstname,
      surname: user.surname,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      createdAt: user.createdAt,
    };

    return NextResponse.json({
      success: true,
      data: userResponse,
      message: 'Account created successfully! You can now sign in.',
    }, { status: 201 });

  } catch (error) {
    console.error('Signup error:', error);

    // Handle duplicate key error (email already exists)
    if (error.code === 11000) {
      return NextResponse.json(
        {
          success: false,
          error: 'User Exists',
          message: 'An account with this email already exists',
        },
        { status: 409 }
      );
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = {};
      Object.keys(error.errors).forEach(key => {
        validationErrors[key] = error.errors[key].message;
      });

      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'Please fix the following errors',
          errors: validationErrors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating your account. Please try again.',
      },
      { status: 500 }
    );
  }
}

// GET method to check if email is available
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing Email',
          message: 'Email parameter is required',
        },
        { status: 400 }
      );
    }

    await connectDB();

    const existingUser = await User.findOne({ email: email.toLowerCase() });
    
    return NextResponse.json({
      success: true,
      available: !existingUser,
      message: existingUser ? 'Email is already taken' : 'Email is available',
    });

  } catch (error) {
    console.error('Email check error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Server Error',
        message: 'Unable to check email availability',
      },
      { status: 500 }
    );
  }
}
