import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Blog } from '@/models/InfoMarker';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/info-markers/[id] - Get single info marker (manager/admin only)
export const GET = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const item = await Blog.findById(id);
    
    if (!item) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Info marker not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching info marker:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/info-markers/[id] - Update info marker (manager/admin only)
export const PUT = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    
    const updatedInfoMarker = await Blog.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    );
    
    if (!updatedInfoMarker) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Info marker not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedInfoMarker,
      message: 'Info marker updated successfully',
    });
  } catch (error) {
    console.error('Error updating info marker:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/info-markers/[id] - Delete info marker (manager/admin only)
export const DELETE = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const deletedInfoMarker = await Blog.findByIdAndDelete(id);
    
    if (!deletedInfoMarker) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Info marker not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: deletedInfoMarker,
      message: 'Info marker deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting info marker:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
