import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import Strip<PERSON> from 'stripe';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request) {
  try {
    const body = await request.text();
    const headersList = headers();
    const signature = headersList.get('stripe-signature');

    if (!signature || !webhookSecret) {
      console.error('Missing Stripe signature or webhook secret');
      return NextResponse.json(
        { error: 'Missing signature or webhook secret' },
        { status: 400 }
      );
    }

    let event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    await connectDB();

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object);
        break;

      case 'charge.dispute.created':
        await handleDisputeCreated(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent) {
  try {
    console.log('Payment succeeded:', paymentIntent.id);

    // Find the booking by payment intent ID
    const booking = await Booking.findOne({
      'payment.stripePaymentIntentId': paymentIntent.id,
    }).populate('customer', 'firstname surname name email')
      .populate('package', 'name');

    if (!booking) {
      console.error('Booking not found for PaymentIntent:', paymentIntent.id);
      return;
    }

    // Update booking payment status
    booking.payment.status = 'paid';
    booking.payment.paidAt = new Date();
    booking.payment.amount = paymentIntent.amount / 100; // Convert from cents
    booking.payment.currency = paymentIntent.currency;

    // Update Stripe details
    if (paymentIntent.charges?.data?.[0]) {
      const charge = paymentIntent.charges.data[0];
      booking.payment.stripeChargeId = charge.id;

      // Store payment method details
      if (charge.payment_method_details?.card) {
        booking.payment.paymentMethod = {
          type: 'card',
          last4: charge.payment_method_details.card.last4,
          brand: charge.payment_method_details.card.brand,
          expMonth: charge.payment_method_details.card.exp_month,
          expYear: charge.payment_method_details.card.exp_year,
        };
      }
    }

    // Confirm the booking
    booking.status = 'confirmed';
    booking.confirmedAt = new Date();

    // Add payment communication record
    await booking.addCommunication({
      type: 'payment',
      direction: 'inbound',
      subject: 'Payment Completed via Webhook',
      content: `Payment of $${booking.payment.amount} completed successfully. Payment Intent: ${paymentIntent.id}`,
      timestamp: new Date(),
    });

    await booking.save();

    console.log('Payment processing completed successfully for booking:', booking.bookingNumber);
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(paymentIntent) {
  try {
    console.log('Payment failed:', paymentIntent.id);

    // Find the booking by payment intent ID
    const booking = await Booking.findOne({
      'payment.stripePaymentIntentId': paymentIntent.id,
    }).populate('customer', 'firstname surname name email')
      .populate('package', 'name');

    if (!booking) {
      console.error('Booking not found for PaymentIntent:', paymentIntent.id);
      return;
    }

    // Update booking payment status
    booking.payment.status = 'failed';
    booking.payment.failedAt = new Date();

    // Store failure details
    if (paymentIntent.last_payment_error) {
      booking.payment.failureReason = {
        code: paymentIntent.last_payment_error.code,
        message: paymentIntent.last_payment_error.message,
        declineCode: paymentIntent.last_payment_error.decline_code,
        type: paymentIntent.last_payment_error.type,
      };
    }

    // Add payment failure communication record
    await booking.addCommunication({
      type: 'payment',
      direction: 'inbound',
      subject: 'Payment Failed',
      content: `Payment failed for booking ${booking.bookingNumber}. Reason: ${paymentIntent.last_payment_error?.message || 'Unknown error'}`,
      timestamp: new Date(),
    });

    await booking.save();

    console.log('Payment failure processing completed for booking:', booking.bookingNumber);
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handlePaymentCanceled(paymentIntent) {
  try {
    console.log('Payment canceled:', paymentIntent.id);

    // Find the booking by payment intent ID
    const booking = await Booking.findOne({
      'payment.stripePaymentIntentId': paymentIntent.id,
    }).populate('customer', 'firstname surname name email')
      .populate('package', 'name');

    if (!booking) {
      console.error('Booking not found for PaymentIntent:', paymentIntent.id);
      return;
    }

    // Update booking payment status
    booking.payment.status = 'cancelled';
    booking.payment.cancelledAt = new Date();

    // Add payment cancellation communication record
    await booking.addCommunication({
      type: 'payment',
      direction: 'inbound',
      subject: 'Payment Cancelled',
      content: `Payment was cancelled for booking ${booking.bookingNumber}. Payment Intent: ${paymentIntent.id}`,
      timestamp: new Date(),
    });

    await booking.save();

    console.log('Payment cancellation processing completed for booking:', booking.bookingNumber);
  } catch (error) {
    console.error('Error handling payment canceled:', error);
  }
}

async function handleDisputeCreated(charge) {
  try {
    console.log('Dispute created for charge:', charge.id);

    const payment = await Payment.findOne({
      'stripe.chargeId': charge.id,
    });

    if (!payment) {
      console.error('Payment record not found for charge:', charge.id);
      return;
    }

    // Update payment with dispute information
    payment.status = 'disputed';
    payment.dispute = {
      amount: charge.amount_disputed,
      reason: charge.dispute?.reason,
      status: charge.dispute?.status,
      disputeId: charge.dispute?.id,
      createdAt: new Date(charge.dispute?.created * 1000),
    };

    await payment.save();

    console.log('Dispute processing completed');
  } catch (error) {
    console.error('Error handling dispute created:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  try {
    console.log('Invoice payment succeeded:', invoice.id);
    // Handle subscription or invoice payments if needed
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleSubscriptionDeleted(subscription) {
  try {
    console.log('Subscription deleted:', subscription.id);
    // Handle subscription cancellations if needed
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}
