'use client'
import { Html, useIntersect } from '@react-three/drei'
import React, { useState, useEffect, useRef, useMemo } from 'react'
// import { useRouter } from 'next/navigation'
import Link from 'next/link'
import ImageScalerComponent from '../ImageScalerComponent'
import { settings } from '@/lib/settings'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

function IconGuides({ id, item, icon}) {
  const refLink = useRef(null)
  const refGroup = useRef(null)
  const [onHover, setOnHover] = useState(false)
  
  // console.log('IconGuides',item)
  return (
    <Link ref={refLink} href={`/360s?id=${item?._360Name}`}
      // onClick={e=>router.push(`/beta/${icon?._360Name}`)}
      onMouseEnter={() => setOnHover(!onHover)}
      onMouseLeave={() => setOnHover(!onHover)}
      className="flex cursor-pointer relative w-fit h-fit items-center justify-center"
    >
      {!onHover
        ? <ImageScalerComponent src={icon?.btnIcons?.off} alt="marker icon" />
        : <ImageScalerComponent src={icon?.btnIcons?.ov} alt="marker icon" />
      }
    </Link>
  )
}

// Create a simpler component that directly sets position without useFrame
const MarkerPosition = ({ position, children }) => {
  const refGroup = useRef(null)
  const [markerVisble, setMarkerVisble] = useState(false)

  useIntersect(refGroup, (isVisible) => {
    // console.log('useIntersect for group:', isVisible);
    if (isVisible) {
      setMarkerVisble(true);
    } else {
      // Optional: Add a timeout here if you want to delay unmounting
      // to avoid rapid unmounting/mounting if the user scrolls quickly.
      // For example, setTimeout(() => setMarkerVisible(false), 300);
      setMarkerVisble(false);
    }
  });
  
  // const refGroup = useIntersect((visible)=>setMarkerVisble(visible)) // `visible` will be true if intersecting
  // Ensure position values are numbers with fallbacks
  const x = typeof position.x === 'number' ? position.x : 0
  const y = typeof position.y === 'number' ? position.y : 0
  const z = typeof position.z === 'number' ? position.z : 0

  // useEffect(() => {
    
  // }, [inView])
  
  
  // console.log('MarkerPosition is marker visble:',markerVisble)

  // Just use the position prop directly on the group
  return (
    <group visible={markerVisble} ref={refGroup} position={[x, y, z]}>
      {children}
    </group>
  )
}

const MarkerIcon = ({ item }) => {
  const [onHover, setOnHover] = useState(false)
  const {experienceState,disptachExperience}=useContextExperience()

  // Ensure item is an object
  const safeItem = item || {} 
    
  // Convert item position to a proper object with numeric values
  // Use parseFloat and handle NaN values with || 0
  const position = {
    x: isNaN(parseFloat(item.x)) ? 0 : parseFloat(item.x),
    y: isNaN(parseFloat(item.y)) ? 0 : parseFloat(item.y),
    z: isNaN(parseFloat(item.z)) ? 0 : parseFloat(item.z)
  }

  const handleEntranceClick = () => {
    console.log('entrance clicked',)
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_NAVBAR})
  }
  
  // Debug output removed to reduce console noise
  // console.log(`MarkerIcon ${safeItem.name || 'unnamed'} position item:`, item)
  // console.log(`MarkerIcon ${safeItem.name || 'unnamed'} controls:`, controls)
  // console.log('MarkerIcon',experienceState)

  return (
    <MarkerPosition position={position}>
      <Html center zIndexRange={[1, 10]}>
        <div
          onPointerOver={() => setOnHover(!onHover)}
          onPointerOut={() => setOnHover(!onHover)}
          className='flex w-fit cursor-pointer h-fit items-center justify-center'
          style={{ pointerEvents: 'auto' }}
        >
          {safeItem.markerType === 'landingPage'
            ? <Link onClick={handleEntranceClick} href={`/360s?id=livingroom_001`}>{settings.markerList.markerTypeIcons.landingPage.btnIcons.off}</Link> :
            safeItem.markerType === 'guide'
              ? <IconGuides 
                  // id={id} 
                  item={item} 
                  icon={settings.markerList.markerTypeIcons.guide} 
                />
              : safeItem.markerType === 'upstairs'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.upstairs} 
                  />
              : safeItem.markerType === 'downstairs'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.downstairs} 
                  />
              : safeItem.markerType === 'infoVideo'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.infoVideo} 
                  />
              : safeItem.markerType === 'infoDoc'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.infoDoc} 
                  />
              : safeItem.markerType === 'infoImage'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.infoImage} 
                  />
              : null
          }
        </div>
      </Html>
    </MarkerPosition>
  )
}

export default function _360InfoMarkers({ markerList}) {
  // Force re-render when markerList changes
  const [, forceUpdate] = useState()

  // Ensure markerList is an array
  const safeMarkerList = Array.isArray(markerList) ? markerList : []

  useEffect(() => {
    // Force a re-render when markerList changes
    forceUpdate({})
  }, [safeMarkerList])

  // console.log('_360InfoMarkers:',markerList)

  return (
    <>
      {/* Markers section */}
      {safeMarkerList.map((item, index) => (
        // Use a unique key based on name and index to ensure proper updates
        <MarkerIcon
          key={`marker-${item?.name || 'unnamed'}-${index}`}
          item={item}
        />
      ))}
    </>
  )
}
