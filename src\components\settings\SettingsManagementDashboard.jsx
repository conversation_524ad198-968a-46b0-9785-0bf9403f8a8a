'use client';

import { settings } from '@/lib/settings';
import { useState, useEffect } from 'react';

function MenuInputFeilds({item,_360Files}) {
  return(
    <div className='flex w-full items-center h-14 gap-2 p-2'>
      <div className='flex w-full items-center h-14 gap-2 shadow border-1 justify-between border-gray-200 p-2 rounded-md'>
        <span className='text-sm capitalize'>{item?.name}: select 360 link</span>
        <select className='flex h-full items-center bg-gray-100 outline-1 rounded-md px-2 text-xs' name="" id="">
          {_360Files.map((item, index) => (
            <option className='text-center max-w-fit text-xs capitalize'  key={item._id || index} value={item?.name}>{item?.name}</option>
          ))}
        </select>
      </div>
    </div>
  )
}

export default function SettingsManagementDashboard() {
  const [_360Files, set_360Files] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    fetch360Files()
  }, [])
  
  const fetch360Files = () => {
    try {
      fetch(`/api/360s`)
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          set_360Files(data.data);
          setIsLoading(false);
        } else {
          setError(data.message || 'Failed to fetch 360° images');
          setIsLoading(false);
        }
      })
      .catch(err => {
        setError('Failed to load 360° images');
        setIsLoading(false);
      });
    } catch (error) {
      console.log(error)
       setError('Failed to load 360° images');
        setIsLoading(false);
    }
  }
  
  const handleSave = (params) => {
    
  }
  
  // Create new functionality removed - only predefined packages can be edited

  return (
    <div className="SettingsManagementDashboard space-y-6 h-full">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className='flex flex-col w-full'>
          <h2 className="text-2xl font-bold text-gray-900">
            Setting Management
          </h2>
          <div className='flex w-full justify-between items-center'>
            <p className="text-gray-600">
              Manage the site settings
            </p>
            <button
              type="button"
              onClick={handleSave}
              disabled={isLoading}
              className="bg-blue-600 w-fit px-4 border-2 text-white py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </div>
              ) : (
                'Save Changes'
              )}
            </button>
          </div>
        </div>
      </div>    

      {/* Create Package Modal removed - only predefined packages can be edited */}
      <form className='flex flex-col w-full h-full'>
        <span className='flex font-medium text-xl capitalize'>menu link asignments</span>
        <div className='flex flex-col gap-2 h-full'>
          <span>asign 360 links to menu buttons</span>
          <div className='relative mt-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 overflow-y-auto g h-fit'>
            {settings.menuPopup.home.map((i,index)=>
              <MenuInputFeilds item={i} _360Files={_360Files} key={index}/>
            )}
            {settings.menuPopup.entrance.map((i,index)=>
              <MenuInputFeilds item={i} _360Files={_360Files} key={index}/>
            )}
            {settings.menuPopup.firstFloor.map((i,index)=>
              <MenuInputFeilds item={i} _360Files={_360Files} key={index}/>
            )}
            {settings.menuPopup.outDoors.map((i,index)=>
              <MenuInputFeilds item={i} _360Files={_360Files} key={index}/>
            )}
            {settings.menuPopup.campOutskirts.map((i,index)=>
              <MenuInputFeilds item={i} _360Files={_360Files} key={index}/>
            )}
          </div>
          
        </div>
      </form>
    </div>
  );
}
