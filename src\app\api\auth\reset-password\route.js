import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';

export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();
    const { token, password, confirmPassword } = body;

    // Validation
    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing Token',
          message: 'Reset token is required',
        },
        { status: 400 }
      );
    }

    if (!password || password.length < 6) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Password',
          message: 'Password must be at least 6 characters',
        },
        { status: 400 }
      );
    }

    if (password !== confirmPassword) {
      return NextResponse.json(
        {
          success: false,
          error: 'Password Mismatch',
          message: 'Passwords do not match',
        },
        { status: 400 }
      );
    }

    // Find user with valid reset token
    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpiry: { $gt: new Date() }, // Token not expired
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Token',
          message: 'Password reset token is invalid or has expired',
        },
        { status: 400 }
      );
    }

    // Update password and clear reset token
    user.password = password; // Will be hashed by pre-save middleware
    user.passwordResetToken = undefined;
    user.passwordResetExpiry = undefined;
    
    await user.save();

    return NextResponse.json({
      success: true,
      message: 'Password has been reset successfully. You can now sign in with your new password.',
    });

  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Server Error',
        message: 'An error occurred while resetting your password. Please try again.',
      },
      { status: 500 }
    );
  }
}

// GET method to verify reset token
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing Token',
          message: 'Reset token is required',
        },
        { status: 400 }
      );
    }

    await connectDB();

    // Find user with valid reset token
    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpiry: { $gt: new Date() }, // Token not expired
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Token',
          message: 'Password reset token is invalid or has expired',
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Token is valid',
      data: {
        email: user.email,
        name: user.name,
      },
    });

  } catch (error) {
    console.error('Token verification error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Server Error',
        message: 'An error occurred while verifying the token',
      },
      { status: 500 }
    );
  }
}
