import React, { Suspense } from 'react'
import _360NavbarComponent from '@/components/360s/_360NavbarComponent';
import _360Navbar from '@/components/360s/_360Navbar';

export default function layout({children}) {
  return (
    <div className='flex flex-grow w-full h-svh items-center justify-center overflow-hidden'>
      <Suspense fallback={null}>
        {/* <_360NavbarComponent/> */}
        <_360Navbar/>
      </Suspense>
      {children}
    </div>
  )
}
