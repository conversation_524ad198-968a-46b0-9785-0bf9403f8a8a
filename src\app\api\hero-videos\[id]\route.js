import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { HeroVideo } from '@/models/HeroVideo';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/hero-videos/[id] - Get single hero video (manager/admin only)
export const GET = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    
    const item = await HeroVideo.findById(id);
    
    if (!item) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Hero video not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching hero video:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch hero video',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/hero-videos/[id] - Update hero video (manager/admin only)
export const PUT = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    
    // If setting as active, deactivate all other videos first
    if (body.isActive) {
      await HeroVideo.updateMany({}, { isActive: false });
    }
    
    const updatedHeroVideo = await HeroVideo.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    );
    
    if (!updatedHeroVideo) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Hero video not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedHeroVideo,
      message: 'Hero video updated successfully',
    });
  } catch (error) {
    console.error('Error updating hero video:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update hero video',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/hero-videos/[id] - Delete hero video (manager/admin only)
export const DELETE = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    
    const deletedHeroVideo = await HeroVideo.findByIdAndDelete(id);
    
    if (!deletedHeroVideo) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Hero video not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: deletedHeroVideo,
      message: 'Hero video deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting hero video:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete hero video',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PATCH /api/hero-videos/[id] - Toggle active status (manager/admin only)
export const PATCH = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const { isActive } = body;
    
    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'isActive must be a boolean value',
        },
        { status: 400 }
      );
    }
    
    // If setting a video to active, deactivate all other videos first
    if (isActive) {
      await HeroVideo.updateMany({}, { isActive: false });
    }
    
    // Update the specified video
    const updatedHeroVideo = await HeroVideo.findByIdAndUpdate(
      id,
      { isActive },
      { new: true }
    );
    
    if (!updatedHeroVideo) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Hero video not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedHeroVideo,
      message: 'Hero video status updated successfully',
    });
  } catch (error) {
    console.error('Error updating hero video status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update hero video status',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
