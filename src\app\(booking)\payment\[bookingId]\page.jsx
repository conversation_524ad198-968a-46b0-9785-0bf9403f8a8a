'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

// Validate Stripe configuration
const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!stripePublishableKey) {
  console.error('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
  console.error('Current environment:', process.env.NODE_ENV);
  console.error('Available env vars:', Object.keys(process.env).filter(key => key.includes('STRIPE')));
}

const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null;

function PaymentForm({ booking, onPaymentSuccess, onPaymentError }) {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);

  // Check if Stripe is properly loaded
  useEffect(() => {
    if (!stripePublishableKey) {
      setError('Payment system configuration error. Please contact support.');
    }
  }, []);

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Create payment intent
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingId: booking._id,
          amount: booking.pricing.totalAmount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const responseData = await response.json();

      if (!responseData.success) {
        throw new Error(responseData.message || 'Failed to create payment intent');
      }

      const { clientSecret } = responseData;

      if (!clientSecret) {
        throw new Error('No client secret received from payment intent');
      }

      // Confirm payment
      const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: {
            card: elements.getElement(CardElement),
            billing_details: {
              name: booking.customer.firstname && booking.customer.surname 
                ? `${booking.customer.firstname} ${booking.customer.surname}`
                : booking.customer.name,
              email: booking.customer.email,
            },
          },
        }
      );

      if (confirmError) {
        throw new Error(confirmError.message);
      }

      if (paymentIntent.status === 'succeeded') {
        // Update booking payment status
        const updateResponse = await fetch(`/api/bookings/${booking._id}/payment`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            paymentIntentId: paymentIntent.id,
            status: 'paid',
            amount: paymentIntent.amount / 100, // Convert from cents
            currency: paymentIntent.currency,
          }),
        });

        const updateData = await updateResponse.json();

        if (!updateData.success) {
          console.error('Failed to update booking payment status:', updateData.message);
          // Still call success handler as payment went through
        }

        onPaymentSuccess(paymentIntent);
      } else if (paymentIntent.status === 'requires_action') {
        // Handle 3D Secure or other authentication
        setError('Payment requires additional authentication. Please try again.');
      } else {
        setError('Payment was not completed. Please try again.');
      }
    } catch (err) {
      setError(err.message);
      onPaymentError(err);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Payment Details</h3>
        <div className="border border-gray-300 rounded-md p-3 bg-white">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
              },
            }}
          />
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className={`w-full py-3 px-4 rounded-md font-medium ${
          !stripe || isProcessing
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        }`}
      >
        {isProcessing ? 'Processing...' : `Pay $${booking.pricing.totalAmount}`}
      </button>
    </form>
  );
}

export default function PaymentPage() {
  const params = useParams();
  const router = useRouter();
  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  useEffect(() => {
    fetchBooking();
  }, [params.bookingId]);

  const fetchBooking = async () => {
    try {
      const response = await fetch(`/api/bookings/${params.bookingId}`);
      const data = await response.json();

      if (data.success) {
        setBooking(data.data);
        
        // Check if payment is already completed
        if (['paid', 'completed'].includes(data.data.payment.status)) {
          setPaymentSuccess(true);
        }
      } else {
        setError(data.message || 'Failed to load booking');
      }
    } catch (err) {
      setError('Failed to load booking');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (paymentIntent) => {
    setPaymentSuccess(true);
    setError(null); // Clear any previous errors

    // Show success message briefly before redirect
    console.log('Payment completed successfully:', paymentIntent.id);

    // Redirect to success page after a shorter delay for better UX
    setTimeout(() => {
      router.push(`/booking-confirmation/${booking._id}?payment=success&intent=${paymentIntent.id}`);
    }, 1500);
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    setError(error.message || 'Payment failed. Please try again.');
    setPaymentSuccess(false);

    // Clear error after 10 seconds to allow retry
    setTimeout(() => setError(null), 10000);
  };

  // Check for Stripe configuration
  if (!stripePromise) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Configuration Error</h1>
          <p className="text-gray-600 mb-4">
            Payment system is not properly configured. Please contact support.
          </p>
          <button
            onClick={() => router.push('/booking')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Back to Booking
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/booking')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Back to Booking
          </button>
        </div>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <div className="text-green-600 text-6xl mb-4">✓</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
          <p className="text-gray-600 mb-4">
            Your booking has been confirmed. You will receive a confirmation email shortly.
          </p>
          <p className="text-sm text-gray-500">
            Redirecting to confirmation page...
          </p>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Booking Not Found</h1>
          <p className="text-gray-600 mb-4">The booking you're looking for doesn't exist.</p>
          <button
            onClick={() => router.push('/booking')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Make New Booking
          </button>
        </div>
      </div>
    );
  }

  const customerName = booking.customer.firstname && booking.customer.surname 
    ? `${booking.customer.firstname} ${booking.customer.surname}`
    : booking.customer.name;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Header */}
          <div className="bg-blue-600 text-white p-6">
            <h1 className="text-2xl font-bold">Complete Your Payment</h1>
            <p className="text-blue-100">Booking #{booking.bookingNumber}</p>
          </div>

          {/* Booking Summary */}
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Booking Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Guest</p>
                <p className="font-medium">{customerName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Package</p>
                <p className="font-medium">{booking.package.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Check-in</p>
                <p className="font-medium">
                  {new Date(booking.dates.checkIn).toLocaleDateString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Check-out</p>
                <p className="font-medium">
                  {new Date(booking.dates.checkOut).toLocaleDateString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Nights</p>
                <p className="font-medium">{booking.dates.duration || 1}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Amount</p>
                <p className="font-bold text-lg text-blue-600">${booking.pricing.totalAmount}</p>
              </div>
            </div>
          </div>

          {/* Payment Form */}
          <div className="p-6">
            <Elements stripe={stripePromise}>
              <PaymentForm
                booking={booking}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentError={handlePaymentError}
              />
            </Elements>
          </div>

          {/* Security Notice */}
          <div className="bg-gray-50 p-4 text-center">
            <p className="text-xs text-gray-500">
              🔒 Your payment information is secure and encrypted
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
