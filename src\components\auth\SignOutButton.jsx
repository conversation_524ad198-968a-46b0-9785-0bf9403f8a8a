'use client';

import { signOut } from 'next-auth/react';
import { useState } from 'react';
import { MdLogout, MdExitToApp } from 'react-icons/md';

export default function SignOutButton({ 
  variant = 'default', 
  className = '',
  showIcon = true,
  showText = true 
}) {
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut({
        callbackUrl: '/auth/signin',
        redirect: true
      });
    } catch (error) {
      console.error('Sign out error:', error);
      setIsSigningOut(false);
    }
  };

  // Different button variants
  const variants = {
    default: 'bg-red-600 hover:bg-red-700 text-white',
    outline: 'border border-red-600 text-red-600 hover:bg-red-50',
    ghost: 'text-red-600 hover:bg-red-50',
    minimal: 'text-gray-600 hover:text-red-600 hover:bg-gray-50'
  };

  const baseClasses = 'inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  return (
    <button
      onClick={handleSignOut}
      disabled={isSigningOut}
      className={`${baseClasses} ${variants[variant]} ${className}`}
      title="Sign out of your account"
    >
      {isSigningOut ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
          {showText && 'Signing out...'}
        </>
      ) : (
        <>
          {showIcon && <MdLogout className={`h-4 w-4 ${showText ? 'mr-2' : ''}`} />}
          {showText && 'Sign Out'}
        </>
      )}
    </button>
  );
}

// Alternative icon-only version for compact spaces
export function SignOutIconButton({ className = '', size = 'md' }) {
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut({
        callbackUrl: '/auth/signin',
        redirect: true
      });
    } catch (error) {
      console.error('Sign out error:', error);
      setIsSigningOut(false);
    }
  };

  const sizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <button
      onClick={handleSignOut}
      disabled={isSigningOut}
      className={`inline-flex items-center justify-center rounded-lg text-gray-600 hover:text-red-600 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${sizeClasses[size]} ${className}`}
      title="Sign out of your account"
    >
      {isSigningOut ? (
        <div className={`animate-spin rounded-full border-b-2 border-current ${iconSizes[size]}`}></div>
      ) : (
        <MdExitToApp className={iconSizes[size]} />
      )}
    </button>
  );
}

// Dropdown menu item version
export function SignOutMenuItem({ onClose }) {
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    if (onClose) onClose();
    
    try {
      await signOut({
        callbackUrl: '/auth/signin',
        redirect: true
      });
    } catch (error) {
      console.error('Sign out error:', error);
      setIsSigningOut(false);
    }
  };

  return (
    <button
      onClick={handleSignOut}
      disabled={isSigningOut}
      className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isSigningOut ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3"></div>
          Signing out...
        </>
      ) : (
        <>
          <MdLogout className="h-4 w-4 mr-3" />
          Sign Out
        </>
      )}
    </button>
  );
}
