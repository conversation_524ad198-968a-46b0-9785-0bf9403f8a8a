import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Rate limiting storage
const rateLimitMap = new Map();

// Clean up old entries every 15 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of rateLimitMap.entries()) {
    if (now - data.resetTime > 15 * 60 * 1000) {
      rateLimitMap.delete(key);
    }
  }
}, 15 * 60 * 1000);

// Rate limiting function
function rateLimit(ip, limit = 100, windowMs = 15 * 60 * 1000) {
  const now = Date.now();
  const key = `${ip}`;
  
  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, {
      count: 1,
      resetTime: now + windowMs,
    });
    return { allowed: true, remaining: limit - 1 };
  }
  
  const data = rateLimitMap.get(key);
  
  if (now > data.resetTime) {
    // Reset the window
    data.count = 1;
    data.resetTime = now + windowMs;
    return { allowed: true, remaining: limit - 1 };
  }
  
  if (data.count >= limit) {
    return { 
      allowed: false, 
      remaining: 0,
      resetTime: data.resetTime 
    };
  }
  
  data.count++;
  return { 
    allowed: true, 
    remaining: limit - data.count 
  };
}

// Protected routes configuration
const protectedRoutes = {
  '/admin': ['admin'],
  '/admin/': ['admin'],
  '/admin/dashboard': ['admin'],
  '/admin/users': ['admin'],
  '/admin/packages': ['admin', 'manager'],
  '/admin/bookings': ['admin', 'manager'],
  '/admin/payments': ['admin', 'manager'],
  '/admin/clients': ['admin', 'manager'],
  '/admin/analytics': ['admin', 'manager'],
  '/dashboard': ['user', 'manager', 'admin'],
  '/profile': ['user', 'manager', 'admin'],
};

// API routes that require authentication
const protectedApiRoutes = {
  '/api/admin': ['admin'],
  '/api/users': ['admin'],
  '/api/packages': {
    GET: ['public'], // Public can view packages
    POST: ['admin', 'manager'],
    PUT: ['admin', 'manager'],
    DELETE: ['admin'],
  },
  '/api/360s': {
    GET: ['public'], // Public can view 360s
    POST: ['admin', 'manager'],
    PUT: ['admin', 'manager'],
    DELETE: ['admin'],
  },
  '/api/bookings': {
    GET: ['user', 'manager', 'admin'], // Users can view their own bookings
    POST: ['public'], // Public can create bookings (guest flow)
    PUT: ['manager', 'admin'],
    DELETE: ['admin'],
  },
  '/api/payments': ['admin', 'manager'],
  '/api/clients': ['admin', 'manager'],
};

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/packages',
  '/packages/',
  '/booking',
  '/booking/',
  '/360s',
  '/360s/',
  '/hero-video',
  '/hero-video/',
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/api/auth',
  '/api/packages', // GET only
  '/api/360s', // GET only - Public 360s viewing
  '/api/hero-videos/active', // GET only - Public active hero video
  '/api/video-gallery/public', // GET only - Public video gallery viewing
];

function isPublicRoute(pathname) {
  return publicRoutes.some(route => {
    if (route.endsWith('/')) {
      return pathname.startsWith(route) || pathname === route.slice(0, -1);
    }
    return pathname === route || pathname.startsWith(route + '/');
  });
}

function getRequiredRoles(pathname, method = 'GET') {
  // Check page routes
  for (const [route, roles] of Object.entries(protectedRoutes)) {
    if (pathname === route || pathname.startsWith(route + '/')) {
      return roles;
    }
  }
  
  // Check API routes
  for (const [route, config] of Object.entries(protectedApiRoutes)) {
    if (pathname.startsWith(route)) {
      if (typeof config === 'object' && !Array.isArray(config)) {
        return config[method] || config.GET || ['admin'];
      }
      return config;
    }
  }
  
  return null;
}

function hasRequiredRole(userRole, requiredRoles) {
  if (!requiredRoles || requiredRoles.includes('public')) {
    return true;
  }
  
  const roleLevels = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };
  
  const userLevel = roleLevels[userRole] || 0;
  
  return requiredRoles.some(role => {
    const requiredLevel = roleLevels[role] || 0;
    return userLevel >= requiredLevel;
  });
}

export default async function middleware(request) {
  const { pathname } = request.nextUrl;
  const method = request.method;
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';

  // Skip rate limiting for specific GET requests to media APIs and static assets
  const isMediaGetRequest = method === 'GET' && (
    pathname.startsWith('/api/hero-videos') ||
    pathname.startsWith('/api/360s') ||
    pathname.startsWith('/api/video-gallery') ||
    pathname.startsWith('/assets/') ||
    pathname.startsWith('/_next/static/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/robots.txt') ||
    pathname.startsWith('/sitemap.xml')
  );

  // Skip rate limiting for essential auth endpoints to prevent login issues
  const isEssentialAuthRoute = pathname.startsWith('/api/auth/signin') ||
                              pathname.startsWith('/api/auth/session') ||
                              pathname.startsWith('/api/auth/providers') ||
                              pathname.startsWith('/api/auth/csrf');

  // Apply rate limiting with different limits for different routes
  const isAuthRoute = pathname.startsWith('/api/auth') || pathname.startsWith('/auth');
  const is360Route = pathname.startsWith('/api/360s') || pathname.startsWith('/360s');

  let rateLimitResult = { allowed: true, remaining: 999 }; // Default for no rate limiting
  let currentLimit = 100; // Default limit - declare in outer scope

  // Skip rate limiting for media requests and essential auth endpoints
  if (!isMediaGetRequest && !isEssentialAuthRoute) {
    if (isAuthRoute) {
      // More lenient limits for auth routes in production to prevent login issues
      currentLimit = process.env.NODE_ENV === 'production' ? 50 : 20;
    } else if (is360Route) {
      currentLimit = 200; // Higher limit for 360s routes due to texture loading
    }

    rateLimitResult = rateLimit(ip, currentLimit);
  }

  if (!rateLimitResult.allowed) {
    return new NextResponse(
      JSON.stringify({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        resetTime: rateLimitResult.resetTime,
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': currentLimit.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString(),
        },
      }
    );
  }

  // Add rate limit headers to response
  const response = NextResponse.next();

  // Add CORS headers for static assets to prevent cross-origin issues
  if (pathname.startsWith('/assets/')) {
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
  }

  if (!isMediaGetRequest && !isEssentialAuthRoute) {
    response.headers.set('X-RateLimit-Limit', currentLimit.toString());
    response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
  } else {
    // For media GET requests and essential auth routes, indicate no rate limiting
    response.headers.set('X-RateLimit-Limit', 'unlimited');
    response.headers.set('X-RateLimit-Remaining', 'unlimited');
  }

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Skip middleware for public routes
  if (isPublicRoute(pathname)) {
    return response;
  }

  // Get required roles for this route
  const requiredRoles = getRequiredRoles(pathname, method);

  // If no specific roles required, allow access
  if (!requiredRoles) {
    return response;
  }

  // Check if user is authenticated using JWT token
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

  if (!token) {
    // For API routes, return 401
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({
          error: 'Unauthorized',
          message: 'Authentication required',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // For page routes, redirect to sign in
    const signInUrl = new URL('/auth/signin', request.url);
    signInUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(signInUrl);
  }

  // Check if user has required role
  const userRole = token.role || 'user';

  if (!hasRequiredRole(userRole, requiredRoles)) {
    // For API routes, return 403
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({
          error: 'Forbidden',
          message: 'Insufficient permissions',
          required: requiredRoles,
          current: userRole,
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // For page routes, redirect to unauthorized page
    return NextResponse.redirect(new URL('/auth/unauthorized', request.url));
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
