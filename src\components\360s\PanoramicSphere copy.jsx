'use client';

import { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import { useControls } from 'leva'

export default function PanoramicSphere({
  currentImage,// This prop holds the camera position and rotation data
  imageUrl,
  imageId,
  textureCache,
  setTextureCache,
  loadingQueue,
  setLoadingQueue,
  onTextureLoad,
}) {
  const meshRef = useRef();
  const controlsRef = useRef();
  const [currentTexture, setCurrentTexture] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  /* console.log('PanoramicSphere - Component Render. Incoming _360Object prop:', _360Object); */

  // Memoize the texture loader to avoid re-creation
  const textureLoader = useMemo(() => new THREE.TextureLoader(), []);

  // Load texture asynchronously
  const loadTexture = useCallback(async (url, id) => {
    /* console.log(`PanoramicSphere - loadTexture called for ID: ${id}, URL: ${url}`); */
    if (textureCache.has(id)) {
      /* console.log(`PanoramicSphere - Texture ID ${id} found in cache.`); */
      return textureCache.get(id);
    }

    setIsLoading(true); // Set loading state to true

    try {
      const texture = await new Promise((resolve, reject) => {
        textureLoader.load( // Use the memoized loader
          url,
          (loadedTexture) => {
            /* console.log(`PanoramicSphere - Texture ID ${id} loaded successfully.`); */
            // Configure texture for panoramic use
            loadedTexture.mapping = THREE.EquirectangularReflectionMapping;
            loadedTexture.wrapS = THREE.RepeatWrapping;
            loadedTexture.wrapT = THREE.ClampToEdgeWrapping;
            loadedTexture.minFilter = THREE.LinearFilter;
            loadedTexture.magFilter = THREE.LinearFilter;
            loadedTexture.flipY = true; // Ensure correct orientation for panoramic images
            resolve(loadedTexture);
          },
          undefined, // onProgress callback (not used here)
          reject // onError callback
        );
      });

      // Cache the loaded texture
      const newCache = new Map(textureCache);
      newCache.set(id, texture);
      setTextureCache(newCache);
      /* console.log(`PanoramicSphere - Texture ID ${id} cached.`); */

      return texture;
    } catch (error) {
      /* console.error(`PanoramicSphere - Error loading texture for ID ${id}:`, error); */
      return null; // Return null on error
    } finally {
      setIsLoading(false); // Reset loading state
      /* console.log(`PanoramicSphere - Finished loading texture for ID: ${id}`); */
    }
  }, [textureCache, setTextureCache, textureLoader]); // Dependencies for loadTexture

  // Background texture loading for queue with status updates
  useEffect(() => {
    /* console.log('PanoramicSphere - useEffect [loadingQueue] triggered. Queue length:', loadingQueue.length); */
    // If the queue is empty, do nothing
    if (loadingQueue.length === 0) return;

    const loadNextTexture = async () => {
      const nextItem = loadingQueue[0];
      if (!nextItem) {
        /* console.log('PanoramicSphere - No next item in queue.'); */
        return;
      }
      if (textureCache.has(nextItem._id)) {
        /* console.log(`PanoramicSphere - Item ${nextItem._id} already cached, removing from queue.`); */
        setLoadingQueue(prev => prev.slice(1));
        return;
      }

      try {
        /* console.log(`PanoramicSphere - Starting background download for ID: ${nextItem._id}`); */
        // Update status to downloading for the current item
        setLoadingQueue(prev =>
          prev.map(item =>
            item._id === nextItem._id
              ? { ...item, status: 'downloading' }
              : item
          )
        );

        // Load the texture
        await loadTexture(nextItem.url, nextItem._id);

        // Update status to cached and remove from queue
        setLoadingQueue(prev =>
          prev.filter(item => item._id !== nextItem._id)
        );
        /* console.log(`PanoramicSphere - Background download for ID: ${nextItem._id} completed.`); */
      } catch (error) {
        /* console.error(`PanoramicSphere - Background texture loading failed for ID ${nextItem._id}:`, error); */
        // On error, remove from queue
        setLoadingQueue(prev =>
          prev.filter(item => item._id !== nextItem._id)
        );
      }
    };

    // Small delay to prevent blocking the main thread during background loading
    const timeoutId = setTimeout(loadNextTexture, 100);
    return () => clearTimeout(timeoutId); // Cleanup timeout on unmount or re-run
  }, [loadingQueue, textureCache, setTextureCache, setLoadingQueue, loadTexture]); // Dependencies for background loading

  // Load current texture when imageUrl or imageId changes
  useEffect(() => {
    /* console.log('PanoramicSphere - useEffect [imageUrl/imageId] triggered. imageUrl:', imageUrl, 'imageId:', imageId); */
    if (!imageUrl || !imageId) {
      /* console.log('PanoramicSphere - imageUrl or imageId missing, skipping current texture load.'); */
      setCurrentTexture(null); // Clear texture if image info is missing
      return; // Exit if essential props are missing
    }

    const loadCurrentTexture = async () => {
      const texture = await loadTexture(imageUrl, imageId);
      if (texture) {
        setCurrentTexture(texture); // Set the loaded texture
        onTextureLoad?.(); // Call the texture load callback if provided
        /* console.log('PanoramicSphere - Current texture set.'); */
      } else {
        setCurrentTexture(null); // Ensure texture is null if loading failed
      }
    };

    loadCurrentTexture(); // Initiate loading of the current texture
  }, [imageUrl, imageId, loadTexture, onTextureLoad]); // Dependencies for current texture loading

  /* console.log(currentImage) */

  // Render nothing if texture is not yet loaded (show loading state)
  if (!currentTexture) {
    /* console.log('PanoramicSphere - No current texture, rendering OrbitControls only.'); */
    return (
      <>
        {/* OrbitControls are still active even when no texture is loaded */}
        <OrbitControls
          ref={controlsRef}
          enableZoom={false}
          enablePan={false}
          enableRotate={true}
          enableDamping={true}
          dampingFactor={0.05}
          rotateSpeed={-0.35}
          minPolarAngle={0}
          maxPolarAngle={Math.PI}
          minAzimuthAngle={-Infinity}
          maxAzimuthAngle={Infinity}
          target-y={currentImage?.cameraPosition || 0}
        />
      </>
    );
  }
  /* console.log('PanoramicSphere - Rendering sphere with texture.'); */

  return (
    <>
      {/* OrbitControls for camera interaction */}
      <OrbitControls
        ref={controlsRef}
        enableZoom={false}
        enablePan={false}
        enableRotate={true}
        enableDamping={true}
        dampingFactor={0.05}
        rotateSpeed={-0.35}
        minPolarAngle={0}
        maxPolarAngle={Math.PI}
        minAzimuthAngle={-Infinity}
        maxAzimuthAngle={Infinity}

      />
      {/* The panoramic sphere mesh */}
      <mesh
        ref={meshRef}
        rotation-y={currentImage?._360Rotation || 0}
        scale={[1, 1, 1]} // Standard scale
      >
        <sphereGeometry args={[32, 60, 40]} /> {/* Sphere geometry: radius, width segments, height segments */}
        <meshBasicMaterial
          map={currentTexture} // Apply the loaded panoramic texture
          side={THREE.BackSide} // Render the texture on the inside of the sphere
          transparent={false} // Not transparent
        />
      </mesh>
    </>
  );
}