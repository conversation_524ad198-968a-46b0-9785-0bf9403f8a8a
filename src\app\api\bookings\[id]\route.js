import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { requireManagerAPI, canAccessResource } from '@/lib/auth-utils';
import { auth } from '@/auth';

// GET /api/bookings/[id] - Get single booking
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const session = await auth();
    
    const booking = await Booking.findOne({
      $or: [
        { _id: id },
        { bookingNumber: id }
      ]
    })
    .populate('customer', 'name email phone address emergencyContact')
    .populate('package', 'name slug category pricing inclusions exclusions')
    .lean();
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    // Check access permissions
    if (session?.user && !['manager', 'admin'].includes(session.user.role)) {
      if (booking.customer._id.toString() !== session.user.id) {
        return NextResponse.json(
          {
            success: false,
            error: 'Forbidden',
            message: 'Access denied',
          },
          { status: 403 }
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      data: booking,
    });
  } catch (error) {
    console.error('Error fetching booking:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/bookings/[id] - Update booking (manager/admin only)
export const PUT = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    delete body.bookingNumber;
    
    const updatedBooking = await Booking.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    )
    .populate('customer', 'name email phone')
    .populate('package', 'name slug category');
    
    if (!updatedBooking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedBooking,
      message: 'Booking updated successfully',
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/bookings/[id] - Cancel booking
export async function DELETE(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const session = await auth();
    
    const booking = await Booking.findById(id);
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    // Check permissions - users can cancel their own bookings, managers can cancel any
    if (session?.user && !['manager', 'admin'].includes(session.user.role)) {
      if (booking.customer.toString() !== session.user.id) {
        return NextResponse.json(
          {
            success: false,
            error: 'Forbidden',
            message: 'Access denied',
          },
          { status: 403 }
        );
      }
    }
    
    // Check if booking can be cancelled
    if (['cancelled', 'checked_out'].includes(booking.status)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Cannot Cancel',
          message: 'Booking cannot be cancelled in current status',
        },
        { status: 409 }
      );
    }
    
    // Update booking status to cancelled
    booking.status = 'cancelled';
    booking.cancellation = {
      cancelledAt: new Date(),
      cancelledBy: session?.user?.id,
      reason: 'Cancelled by user',
    };
    
    await booking.save();
    
    return NextResponse.json({
      success: true,
      message: 'Booking cancelled successfully',
      data: { id: booking._id, status: booking.status },
    });
  } catch (error) {
    console.error('Error cancelling booking:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to cancel booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/bookings/[id] - Partial update booking
export const PATCH = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const { action, ...data } = body;
    
    const booking = await Booking.findById(id);
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    let updateOperation = {};
    
    switch (action) {
      case 'confirm':
        updateOperation = {
          status: 'confirmed',
        };
        break;
        
      case 'check_in':
        updateOperation = {
          status: 'checked_in',
          'checkInOut.checkInTime': new Date(),
          'checkInOut.checkInNotes': data.notes,
          'checkInOut.checkedInBy': data.staffId,
        };
        break;
        
      case 'check_out':
        updateOperation = {
          status: 'checked_out',
          'checkInOut.checkOutTime': new Date(),
          'checkInOut.checkOutNotes': data.notes,
          'checkInOut.checkedOutBy': data.staffId,
        };
        break;
        
      case 'add_communication':
        updateOperation = {
          $push: {
            communications: {
              type: data.type,
              direction: data.direction,
              subject: data.subject,
              content: data.content,
              author: data.authorId,
              timestamp: new Date(),
            },
          },
        };
        break;
        
      case 'add_note':
        updateOperation = {
          $push: {
            internalNotes: {
              content: data.content,
              author: data.authorId,
              createdAt: new Date(),
              isPrivate: data.isPrivate || true,
            },
          },
        };
        break;
        
      case 'update_payment_status':
        updateOperation = {
          'payment.status': data.status,
          'payment.paidAmount': data.paidAmount,
          'payment.remainingAmount': data.remainingAmount,
        };
        break;
        
      default:
        // Direct field updates
        updateOperation = data;
    }
    
    const updatedBooking = await Booking.findByIdAndUpdate(
      id,
      updateOperation,
      { 
        new: true, 
        runValidators: true 
      }
    )
    .populate('customer', 'name email phone')
    .populate('package', 'name slug category');
    
    return NextResponse.json({
      success: true,
      data: updatedBooking,
      message: 'Booking updated successfully',
    });
  } catch (error) {
    console.error('Error patching booking:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
