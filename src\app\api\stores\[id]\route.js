import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Gallery } from '@/models/Store';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/stores/[id] - Get single store item (manager/admin only)
export const GET = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    
    const item = await Gallery.findById(id);
    
    if (!item) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Store item not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching store item:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch store item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/stores/[id] - Update store item (manager/admin only)
export const PUT = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    
    // Ensure image is an array if provided
    if (body.image && !Array.isArray(body.image)) {
      body.image = [body.image];
    }
    
    const updatedStoreItem = await Gallery.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    );
    
    if (!updatedStoreItem) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Store item not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedStoreItem,
      message: 'Store item updated successfully',
    });
  } catch (error) {
    console.error('Error updating store item:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update store item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/stores/[id] - Delete store item (manager/admin only)
export const DELETE = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    
    const deletedStoreItem = await Gallery.findByIdAndDelete(id);
    
    if (!deletedStoreItem) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Store item not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: deletedStoreItem,
      message: 'Store item deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting store item:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete store item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
