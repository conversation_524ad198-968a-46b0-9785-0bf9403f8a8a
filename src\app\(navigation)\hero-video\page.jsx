import React from 'react';
import Link from 'next/link';
import HeroVideoClient from './HeroVideoClient';
import { settings } from '@/lib/settings';

// Metadata for the page
export const metadata = {
  title: 'Hero Video',
  description: 'Full screen video',
};

export default async function page() {
  let videoData = null;

  try {
    // Fetch active hero video data with error handling
    const response = await fetch(`${settings.url}/api/hero-videos/active`, {
      next: { revalidate: 60 } // Cache for 60 seconds
    });

    if (response.ok) {
      const result = await response.json();
      if (result.success) {
        videoData = result.data;
      }
    }
  } catch (error) {
    // Silently handle fetch errors - the client component will handle the null videoPath
    console.log('Error fetching hero video:', error.message);
  }

  return (
    <div className='flex w-full h-screen items-center justify-center overflow-hidden'>
      <Link href='/360s?id=entrance_360' className='flex z-50 w-fit h-10 border-2 bg-black/30 text-white items-center border-r-gray-50 rounded-full px-6 absolute top-10 left-0 right-0 capitalize mx-auto'>skip</Link>
      <HeroVideoClient videoPath={videoData?.url} videoData={videoData} />
    </div>
  );
}
