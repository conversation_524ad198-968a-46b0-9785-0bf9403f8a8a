'use client'

import Image from 'next/image'
import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import { settings } from '@/lib/settings'

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function LandingpageComponent() {
    const router=useRouter()

    const handleOnExplore = () => {
        router.push('/hero-video')
        // console.log('explore')
    }
    const handleOnBookNow = () => {
        router.push('/booking')
    }
    // console.log('LandingpageComponent:')
  return (
    <div className="flex flex-col items-center select-none absolute text-center left-0 right-0 mx-auto bg-black/75' justify-between w-fit h-svh bg-black/25' p-2">
        <div className="flex relative mt-[7.5%] max-w-fit max-h-fit">
            <Image priority width={271} height={271} alt='elephant sands logo' src={settings.landingPage.logo}/>
        </div>

        <div className="flex relative md:bottom-24 bottom-16 items-center justify-between flex-col w-full h-[300px] bg-black/35'">
            <div className="flex relative mt-3 items-center flex-col uppercase mb-2">
                <span className="text-4xl">welcome to <span className='font-bold'>elephant island</span></span>
                <span className="font-bold tracking-wider text-lg">your home away from home experience</span>
            </div>
            <div className='flex flex-col items-center justify-center w-full h-fit'>
                <div className='flex items-center justify-center w-fit md:mt-5 h-fit'>
                    {settings.landingPage.list.map((i,index)=>
                        // console.log(i)
                        <BtnLandingpageComponent 
                            key={index} 
                            data={i}
                            index={index}
                        />
                    )}
                </div>

                <div className='flex items-center justify-center w-fit md:h-40 h-26'>
                    {settings.landingPage.btns.map((i,index)=>
                        // console.log(i)
                        <BtnLandingpageComponent 
                            fn={[handleOnExplore,handleOnBookNow]} 
                            index={index}
                            key={index} 
                            data={i}
                        />
                    )}
                </div>
            </div>
        </div>
    </div>
  )
}
