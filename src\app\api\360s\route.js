import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/360s - Get all 360s with search and filtering (public access)
export async function GET(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-priority';
    const id = searchParams.get('id'); // For specific 360 lookup by name

    // Build query
    const query = {};

    // If specific ID/name is requested
    if (id) {
      query.$or = [
        { _id: id },
        { name: id },
        { originalFileName: id }
      ];
    }

    // Search by name or originalFileName
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { originalFileName: { $regex: search, $options: 'i' } },
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await _360Settings.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await _360Settings.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching 360s:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch 360s',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/360s - Create new 360 (manager/admin only)
export const POST = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'url'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }
    
    // Create new 360
    const new360 = new _360Settings(body);
    await new360.save();
    
    return NextResponse.json(
      {
        success: true,
        data: new360,
        message: '360 created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating 360:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// Shared update logic for both PUT and PATCH methods
const handleBulkUpdate = async (request) => {
  try {
    await connectDB();

    const body = await request.json();
    const { items, action } = body;

    if (!Array.isArray(items)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'items must be an array',
        },
        { status: 400 }
      );
    }

    const results = [];

    for (const itemData of items) {
      try {
        const { _id, ...updateData } = itemData;

        // Handle bulk actions
        if (action) {
          switch (action) {
            case 'update_priority':
              // Priority updates handled individually
              break;
            default:
              // Direct field updates
              break;
          }
        }

        const updated360 = await _360Settings.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        );

        if (updated360) {
          results.push({
            id: _id,
            success: true,
            data: updated360,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: '360 not found',
          });
        }
      } catch (error) {
        results.push({
          id: itemData._id,
          success: false,
          error: error.message,
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating 360s:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update 360s',
        message: error.message,
      },
      { status: 500 }
    );
  }
};

// PUT /api/360s - Bulk update 360s (complete replacement, manager/admin only)
export const PUT = requireManagerAPI(handleBulkUpdate);

// PATCH /api/360s - Bulk partial update 360s (partial updates like markers, manager/admin only)
export const PATCH = requireManagerAPI(handleBulkUpdate);

// DELETE /api/360s - Bulk delete 360s (manager/admin only)
export const DELETE = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { ids } = body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'ids must be a non-empty array',
        },
        { status: 400 }
      );
    }
    
    const result = await _360Settings.deleteMany({
      _id: { $in: ids }
    });
    
    return NextResponse.json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        requestedCount: ids.length,
      },
      message: `${result.deletedCount} 360s deleted successfully`,
    });
  } catch (error) {
    console.error('Error deleting 360s:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete 360s',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
