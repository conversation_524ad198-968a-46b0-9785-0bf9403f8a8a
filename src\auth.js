import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
import CredentialsProvider from 'next-auth/providers/credentials';
import { MongoDBAdapter } from '@auth/mongodb-adapter';
import { MongoClient } from 'mongodb';
import bcrypt from 'bcryptjs';
import connectDB from '@/lib/mongodb';

const client = new MongoClient(process.env.MONGODB_URI, {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  family: 4, // Use IPv4, skip trying IPv6
  retryWrites: true,
  w: 'majority'
});

const clientPromise = client.connect().catch((error) => {
  console.error('Auth MongoDB connection error:', error);
  throw error;
});

const {
  handlers,
  auth,
  signIn,
  signOut,
} = NextAuth({
  adapter: MongoDBAdapter(clientPromise),

  // Configure trusted hosts for production and development
  trustHost: true, // Allow any host for flexibility

  // Set the base URL dynamically based on environment
  url: process.env.NEXTAUTH_URL ||
       (process.env.NODE_ENV === 'production'
         ? 'https://victorchelemu.com'
         : 'http://localhost:3001'),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          await connectDB();
          const { User } = await import('@/models/User');
          
          const user = await User.findOne({ email: credentials.email });
          
          if (!user || !user.password) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
          
          if (!isPasswordValid) {
            return null;
          }

          // Update login tracking
          await User.findByIdAndUpdate(user._id, {
            $inc: { loginCount: 1 },
            lastLogin: new Date(),
          });

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            image: user.image,
            role: user.role,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'database',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },

  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        await connectDB();
        const { User } = await import('@/models/User');

        // Auto-assign admin role to specific emails
        const adminEmails = [
          process.env.ADMIN_EMAIL,
          '<EMAIL>',
          '<EMAIL>'
        ].filter(Boolean); // Remove any undefined values

        // Debug logging for production troubleshooting
        if (process.env.NODE_ENV === 'development') {
          console.log('SignIn attempt:', {
            email: user.email,
            provider: account?.provider,
            adminEmails,
            isAdmin: adminEmails.includes(user.email)
          });
        }

        if (adminEmails.includes(user.email)) {
          user.role = 'admin';
        }

        // For OAuth providers, create or update user
        if (account?.provider !== 'credentials') {
          const existingUser = await User.findOne({ email: user.email });

          if (existingUser) {
            // Update existing user
            await User.findByIdAndUpdate(existingUser._id, {
              $inc: { loginCount: 1 },
              lastLogin: new Date(),
              role: adminEmails.includes(user.email) ? 'admin' : existingUser.role,
            });
          } else {
            // Create new user for OAuth
            await User.create({
              email: user.email,
              name: user.name,
              image: user.image,
              role: adminEmails.includes(user.email) ? 'admin' : 'user',
              provider: account.provider,
              loginCount: 1,
              lastLogin: new Date(),
            });
          }
        }

        return true;
      } catch (error) {
        console.error('SignIn callback error:', error);
        return false;
      }
    },
    async session({ session, user }) {
      if (session?.user) {
        try {
          await connectDB();
          const { User } = await import('@/models/User');

          const dbUser = await User.findOne({ email: session.user.email });

          if (dbUser) {
            session.user.id = dbUser._id.toString();
            session.user.role = dbUser.role;
            session.user.loginCount = dbUser.loginCount;
            session.user.isGuest = dbUser.isGuest || false;
          }
        } catch (error) {
          console.error('Session callback error:', error);
        }
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Get the correct base URL for the environment
      const correctBaseUrl = process.env.NEXTAUTH_URL ||
                            (process.env.NODE_ENV === 'production'
                              ? 'https://victorchelemu.com'
                              : 'https://localhost:3001');

      // Redirect to admin dashboard after successful sign in
      if (url === baseUrl || url === `${baseUrl}/` || url === correctBaseUrl || url === `${correctBaseUrl}/`) {
        return `${correctBaseUrl}/admin/dashboard`;
      }

      // Allow relative callback URLs
      if (url.startsWith('/')) {
        return `${correctBaseUrl}${url}`;
      }

      // Allow callback URLs on the same origin
      try {
        const urlObj = new URL(url);
        const baseUrlObj = new URL(correctBaseUrl);
        if (urlObj.origin === baseUrlObj.origin) {
          return url;
        }
      } catch (error) {
        console.error('Redirect URL parsing error:', error);
      }

      return `${correctBaseUrl}/admin/dashboard`;
    },
  },
  debug: process.env.NODE_ENV === 'development',
});

export { handlers, auth, signIn, signOut };
