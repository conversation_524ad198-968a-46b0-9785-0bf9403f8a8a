import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { VideoGallery } from '@/models/VideoGallery';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/video-gallery/[id] - Get single video gallery item (manager/admin only)
export const GET = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    
    const item = await VideoGallery.findById(id);
    
    if (!item) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Video gallery item not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching video gallery item:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch video gallery item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/video-gallery/[id] - Update video gallery item (manager/admin only)
export const PUT = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    
    const updatedVideoGalleryItem = await VideoGallery.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    );
    
    if (!updatedVideoGalleryItem) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Video gallery item not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedVideoGalleryItem,
      message: 'Video gallery item updated successfully',
    });
  } catch (error) {
    console.error('Error updating video gallery item:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update video gallery item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/video-gallery/[id] - Delete video gallery item (manager/admin only)
export const DELETE = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();

    const { id } = await params;
    
    const deletedVideoGalleryItem = await VideoGallery.findByIdAndDelete(id);
    
    if (!deletedVideoGalleryItem) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Video gallery item not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: deletedVideoGalleryItem,
      message: 'Video gallery item deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting video gallery item:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete video gallery item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
