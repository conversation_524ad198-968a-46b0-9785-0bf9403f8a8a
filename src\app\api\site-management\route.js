import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Site } from '@/models/Site';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/site-management - Get current site settings (manager/admin only)
export const GET = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    // Get or create default site settings
    const site = await Site.getOrCreateDefault();
    
    return NextResponse.json({
      success: true,
      data: site,
      message: 'Site settings retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch site settings',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// POST /api/site-management - Create new site settings (admin only)
export const POST = requireManagerAPI(async (request) => {
  try {
    await connectDB();

    const body = await request.json();
    const { menulinks, configuration, landingPageSettings, markerSettings } = body;

    // Validate required menulinks field
    if (!menulinks || typeof menulinks !== 'object') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'menulinks field is required and must be an object',
        },
        { status: 400 }
      );
    }

    // Validate menulinks structure
    const requiredKeys = ['home', 'entrance', 'firstFloor', 'outDoors', 'campOutskirts'];
    for (const key of requiredKeys) {
      if (!Array.isArray(menulinks[key])) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `menulinks.${key} must be an array`,
          },
          { status: 400 }
        );
      }
    }

    // Deactivate existing active site
    await Site.updateMany({ isActive: true }, { isActive: false });

    // Create new site settings
    const newSite = new Site({
      menulinks,
      configuration,
      landingPageSettings,
      markerSettings,
      lastModifiedBy: request.user?.id,
      isActive: true
    });

    await newSite.save();

    return NextResponse.json({
      success: true,
      data: newSite,
      message: 'Site settings created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating site settings:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: Object.values(error.errors).map(err => err.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create site settings',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/site-management - Update site settings (manager/admin only)
export const PUT = requireManagerAPI(async (request) => {
  try {
    await connectDB();

    const body = await request.json();
    const { menulinks, configuration, landingPageSettings, markerSettings } = body;

    // Get current active site
    const site = await Site.getOrCreateDefault();

    // Update fields if provided
    if (menulinks) {
      // Validate menulinks structure
      const requiredKeys = ['home', 'entrance', 'firstFloor', 'outDoors', 'campOutskirts'];
      for (const key of requiredKeys) {
        if (menulinks[key] !== undefined && !Array.isArray(menulinks[key])) {
          return NextResponse.json(
            {
              success: false,
              error: 'Validation Error',
              message: `menulinks.${key} must be an array`,
            },
            { status: 400 }
          );
        }
      }
      
      site.updateMenuLinks(menulinks);
    }

    if (configuration) {
      site.configuration = { ...site.configuration, ...configuration };
    }

    if (landingPageSettings) {
      site.landingPageSettings = { ...site.landingPageSettings, ...landingPageSettings };
    }

    if (markerSettings) {
      site.markerSettings = { ...site.markerSettings, ...markerSettings };
    }

    site.lastModifiedBy = request.user?.id;
    await site.save();

    return NextResponse.json({
      success: true,
      data: site,
      message: 'Site settings updated successfully'
    });

  } catch (error) {
    console.error('Error updating site settings:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: Object.values(error.errors).map(err => err.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update site settings',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/site-management - Reset to default settings (admin only)
export const DELETE = requireManagerAPI(async (request) => {
  try {
    await connectDB();

    // Check if user is admin (more restrictive for delete operations)
    if (request.user?.role !== 'admin') {
      return NextResponse.json(
        {
          success: false,
          error: 'Forbidden',
          message: 'Admin access required for reset operation',
        },
        { status: 403 }
      );
    }

    // Deactivate all existing sites
    await Site.updateMany({}, { isActive: false });

    // Create new default site
    const defaultSite = await Site.getOrCreateDefault();

    return NextResponse.json({
      success: true,
      data: defaultSite,
      message: 'Site settings reset to default successfully'
    });

  } catch (error) {
    console.error('Error resetting site settings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to reset site settings',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
