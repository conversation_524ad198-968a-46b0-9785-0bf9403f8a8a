'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import LoadingComponent from '@/components/LoadingComponent';

// Helper function to determine video type from URL
function getVideoType(url) {
  if (!url) return 'video/mp4'; // Default type

  try {
    // For Firebase Storage URLs that don't have file extensions in the URL
    if (url.includes('firebasestorage.googleapis.com')) {
      // Check if there's a token parameter that might contain the file name
      const fileNameMatch = url.match(/([^/?&]+\.(mp4|webm|ogg|mov))/i);
      if (fileNameMatch) {
        const extension = fileNameMatch[2].toLowerCase();
        return `video/${extension === 'mov' ? 'mp4' : extension}`;
      }

      // If we can't determine from URL, default to mp4
      return 'video/mp4';
    }

    // For regular URLs with file extensions
    const extension = url.split('.').pop().toLowerCase();
    switch (extension) {
      case 'mp4':
        return 'video/mp4';
      case 'webm':
        return 'video/webm';
      case 'ogg':
        return 'video/ogg';
      case 'mov':
        return 'video/mp4'; // Most browsers handle .mov files as mp4
      default:
        return 'video/mp4'; // Default to mp4
    }
  } catch (error) {
    // Silently handle any errors and return default type
    return 'video/mp4';
  }
}

export default function VideoStream({
  src,
  autoPlay = true,
  loop = false, // Changed default to false so we can redirect when video ends
  muted = true,
  controls = false,
  className = '',
  poster = '',
  preload = 'auto', // Used directly in video element
  playsInline = true, // Used directly in video element
  onEnded = null, // New prop for custom end behavior
  onError = null, // New prop for error handling
  redirectTo = null // New prop for redirect path
}) {
  const videoRef = useRef(null);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  // Safely handle video errors
  const handleError = (error) => {
    setIsLoading(false);

    // Call custom error handler if provided
    if (typeof onError === 'function') {
      onError(error);
    }
  };

  // Handle autoplay on mount with enhanced reliability for production
  useEffect(() => {
    if (!src) {
      setIsLoading(false);
      return;
    }

    const videoElement = videoRef.current;
    if (!videoElement) return;

    // Production-ready autoplay handling with multiple fallback strategies
    if (autoPlay) {
      let playAttempts = 0;
      const maxPlayAttempts = 3;

      // Set up event listeners for better autoplay handling
      const handleCanPlay = () => {
        if (playAttempts >= maxPlayAttempts) {
          setIsLoading(false);
          return;
        }

        playAttempts++;

        try {
          // Ensure video is muted for better autoplay chances
          videoElement.muted = true;

          // Try to play the video as soon as it can play
          const playPromise = videoElement.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                // Autoplay started successfully
                setIsLoading(false);
                playAttempts = maxPlayAttempts; // Stop further attempts
              })
              .catch((error) => {
                // Autoplay was prevented - try different strategies
                console.log(`Play attempt ${playAttempts} failed:`, error.name);

                if (playAttempts < maxPlayAttempts) {
                  // Strategy 1: Wait a bit and try again
                  setTimeout(() => {
                    if (videoElement && !videoElement.paused) return;

                    // Strategy 2: Reset video and try again
                    videoElement.currentTime = 0;
                    videoElement.load();
                  }, 500 * playAttempts); // Increasing delay
                } else {
                  setIsLoading(false);
                  // Final fallback: redirect after failed attempts
                  if (redirectTo) {
                    setTimeout(() => {
                      router.push(redirectTo);
                    }, 2000);
                  }
                }
              });
          } else {
            setIsLoading(false);
          }
        } catch (error) {
          console.log('Video play error:', error);
          setIsLoading(false);
        }
      };

      // Add event listeners for loading/buffering states
      const handleWaiting = () => {
        setIsLoading(true); // Video is buffering/waiting for more data
      };

      const handlePlaying = () => {
        setIsLoading(false); // Video is playing again after buffering
      };

      // Define event handlers for loadstart and canplaythrough
      const handleLoadStart = () => setIsLoading(true);
      const handleCanPlayThrough = () => {
        setIsLoading(false);
        // Try to play when video is fully loaded
        if (playAttempts === 0) {
          handleCanPlay();
        }
      };

      // Handle video errors with production-ready fallbacks
      const handleVideoError = (e) => {
        console.log('Video error occurred:', e.target.error);

        // Try to recover from network errors
        if (e.target.error && e.target.error.code === MediaError.MEDIA_ERR_NETWORK) {
          if (playAttempts < maxPlayAttempts) {
            console.log('Network error - attempting to reload video');
            setTimeout(() => {
              videoElement.load();
            }, 1000);
            return;
          }
        }

        handleError(e);

        // Redirect on persistent errors
        if (redirectTo) {
          setTimeout(() => {
            router.push(redirectTo);
          }, 1500);
        }
      };

      // Production-ready event listeners with error handling
      const addEventListeners = () => {
        try {
          videoElement.addEventListener('canplay', handleCanPlay);
          videoElement.addEventListener('waiting', handleWaiting);
          videoElement.addEventListener('playing', handlePlaying);
          videoElement.addEventListener('loadstart', handleLoadStart);
          videoElement.addEventListener('canplaythrough', handleCanPlayThrough);
          videoElement.addEventListener('error', handleVideoError);

          // Additional production events
          videoElement.addEventListener('stalled', () => {
            console.log('Video stalled - network issues detected');
            setIsLoading(true);
          });

          videoElement.addEventListener('suspend', () => {
            console.log('Video loading suspended');
          });
        } catch (error) {
          console.log('Error adding event listeners:', error);
        }
      };

      addEventListeners();

      // Try to play immediately if video is ready
      if (videoElement.readyState >= 3) { // HAVE_FUTURE_DATA or higher
        handleCanPlay();
      } else if (videoElement.readyState >= 1) { // HAVE_METADATA
        // Video metadata is loaded, start loading
        setIsLoading(true);
      }

      return () => {
        // Cleanup: remove all event listeners and pause video when component unmounts
        try {
          videoElement.removeEventListener('canplay', handleCanPlay);
          videoElement.removeEventListener('waiting', handleWaiting);
          videoElement.removeEventListener('playing', handlePlaying);
          videoElement.removeEventListener('loadstart', handleLoadStart);
          videoElement.removeEventListener('canplaythrough', handleCanPlayThrough);
          videoElement.removeEventListener('error', handleVideoError);
          videoElement.removeEventListener('stalled', () => {});
          videoElement.removeEventListener('suspend', () => {});

          // Pause and reset video
          videoElement.pause();
          videoElement.currentTime = 0;
        } catch (error) {
          // Silently handle cleanup errors
          console.log('Video cleanup error:', error);
        }
      };
    } else {
      setIsLoading(false);
    }

    return () => {
      // Cleanup: pause video when component unmounts
      if (videoElement) {
        try {
          videoElement.pause();
        } catch (error) {
          // Silently handle pause errors
        }
      }
    };
  }, [autoPlay, src, onError, redirectTo, router]);

  // Handle video end event
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleEnded = () => {
      // If custom onEnded handler is provided, call it
      if (typeof onEnded === 'function') {
        onEnded();
      }

      // If redirectTo path is provided, navigate to that path
      if (redirectTo) {
        router.push(redirectTo);
      }
    };

    // Add event listener for the 'ended' event
    videoElement.addEventListener('ended', handleEnded);

    // Clean up the event listener when component unmounts
    return () => {
      videoElement.removeEventListener('ended', handleEnded);
    };
  }, [onEnded, redirectTo, router]);

  // Force muted for better autoplay chances
  // Most browsers require muted videos for autoplay
  const forceMuted = autoPlay ? true : muted;

  // Don't render anything if no source is provided
  if (!src) {
    return null;
  }

  return (
    <div className="relative w-full h-full">
      <video
        ref={videoRef}
        className={`w-full h-full object-cover ${className}`}
        autoPlay={autoPlay}
        loop={loop}
        muted={forceMuted}
        controls={controls}
        poster={poster}
        preload={preload}
        playsInline={playsInline}
        src={src}
        onError={handleError}
        style={{
          // Ensure video covers the entire container
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
      >
        {/* Fallback for browsers that need source tag */}
        <source src={src} type={getVideoType(src)} />
        Your browser does not support the video tag.
      </video>

      {/* Show loading component when video is loading/buffering */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
          <LoadingComponent />
        </div>
      )}
    </div>
  );
}
