import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
import CredentialsProvider from 'next-auth/providers/credentials';

// Fallback auth configuration for when MongoDB is unavailable
// This uses JWT strategy instead of database sessions

const adminEmails = [
  process.env.ADMIN_EMAIL,
  '<EMAIL>',
  'josh<PERSON><EMAIL>'
].filter(Boolean);

const {
  handlers,
  auth,
  signIn,
  signOut,
} = NextAuth({
  // Use JWT strategy instead of database when MongoDB is unavailable
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // For fallback mode, only allow admin users with a simple password check
        if (adminEmails.includes(credentials.email) && credentials.password === 'admin123') {
          return {
            id: credentials.email,
            email: credentials.email,
            name: credentials.email.split('@')[0],
            role: 'admin',
          };
        }

        return null;
      },
    }),
  ],

  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },

  callbacks: {
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token and role to the token right after signin
      if (user) {
        token.role = adminEmails.includes(user.email) ? 'admin' : 'user';
        token.id = user.id || user.email;
      }
      return token;
    },

    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role || 'user';
        session.user.loginCount = 1; // Default for fallback mode
        session.user.isGuest = false;
      }
      return session;
    },

    async signIn({ user, account, profile }) {
      // Auto-assign admin role to specific emails
      if (adminEmails.includes(user.email)) {
        user.role = 'admin';
      }
      return true;
    },

    async redirect({ url, baseUrl }) {
      // Redirect to admin dashboard after successful sign in
      if (url === baseUrl || url === `${baseUrl}/`) {
        return `${baseUrl}/admin/dashboard`;
      }
      // Allow relative callback URLs
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }
      // Allow callback URLs on the same origin
      if (new URL(url).origin === baseUrl) {
        return url;
      }
      return `${baseUrl}/admin/dashboard`;
    },
  },

  debug: process.env.NODE_ENV === 'development',
});

export { handlers, auth, signIn, signOut };
