'use client';

import { useState, useEffect, useCallback } from 'react';
import BookingCalendar from './BookingCalendar';
import BookingList from './BookingList';
import BookingDetails from './BookingDetails';
import BookingFilters from './BookingFilters';
import BookingStats from './BookingStats';
import CheckInOutManager from './CheckInOutManager';

export default function BookingManagementDashboard() {
  const [bookings, setBookings] = useState([]);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('calendar'); // 'calendar' | 'list' | 'details' | 'checkin'
  
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    paymentStatus: '',
    package: '',
    dateRange: '',
    sort: '-createdAt',
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    pages: 0,
  });

  const [dateRange, setDateRange] = useState({
    start: new Date(),
    end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  });

  const [autoRefresh, setAutoRefresh] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    fetchBookings();
  }, [filters, pagination.page, dateRange]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchBookings();
      setLastRefresh(new Date());
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, filters, pagination.page, dateRange]);

  const fetchBookings = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        startDate: dateRange.start.toISOString(),
        endDate: dateRange.end.toISOString(),
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v)),
      });

      const response = await fetch(`/api/bookings?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setBookings(data.data);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total,
          pages: data.pagination.pages,
        }));
        setLastRefresh(new Date());
      } else {
        setError(data.message || 'Failed to fetch bookings');
      }
    } catch (err) {
      console.error('Error fetching bookings:', err);
      setError(`Failed to load bookings: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.page, pagination.limit, dateRange, filters]);

  const handleBookingSelect = async (booking) => {
    setIsLoading(true);
    try {
      // Fetch full booking details
      const response = await fetch(`/api/bookings/${booking._id || booking.bookingNumber}`);
      const data = await response.json();

      if (data.success) {
        setSelectedBooking(data.data);
        setView('details');
      } else {
        setError('Failed to load booking details');
      }
    } catch (err) {
      setError('Failed to load booking details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBookingUpdate = (updatedBooking) => {
    // Update booking in the list
    setBookings(prev => 
      prev.map(booking => 
        booking._id === updatedBooking._id ? updatedBooking : booking
      )
    );
    
    // Update selected booking if it's the same one
    if (selectedBooking && selectedBooking._id === updatedBooking._id) {
      setSelectedBooking(updatedBooking);
    }
  };

  const handleBookingDelete = (deletedBookingId) => {
    setBookings(prev => prev.filter(booking => booking._id !== deletedBookingId));
    if (selectedBooking && selectedBooking._id === deletedBookingId) {
      setSelectedBooking(null);
      setView('calendar');
    }
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleDateRangeChange = (newDateRange) => {
    setDateRange(newDateRange);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleBackToCalendar = () => {
    setSelectedBooking(null);
    setView('calendar');
  };

  const handleCheckInOut = () => {
    setView('checkin');
  };

  const getViewTitle = () => {
    switch (view) {
      case 'calendar':
        return 'Booking Calendar';
      case 'list':
        return 'Booking List';
      case 'details':
        return `Booking Details - ${selectedBooking?.bookingNumber}`;
      case 'checkin':
        return 'Check-In / Check-Out';
      default:
        return 'Booking Management';
    }
  };

  const getViewDescription = () => {
    switch (view) {
      case 'calendar':
        return 'Visual calendar view of all bookings and availability';
      case 'list':
        return 'Detailed list view with advanced filtering and search';
      case 'details':
        return `Managing booking for ${selectedBooking?.customer?.name}`;
      case 'checkin':
        return 'Manage guest check-ins and check-outs';
      default:
        return 'Comprehensive booking management system';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {getViewTitle()}
          </h2>
          <p className="text-gray-600">
            {getViewDescription()}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Auto-refresh toggle and status */}
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300  focus:ring-gray-500"
              />
              <span>Auto-refresh</span>
            </label>
            <span className="text-gray-400">•</span>
            <span>
              Last updated: {isClient ? lastRefresh.toLocaleTimeString() : '--:--:--'}
            </span>
          </div>

          {view === 'details' && (
            <button
              onClick={handleBackToCalendar}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              ← Back to Calendar
            </button>
          )}

          {view === 'checkin' && (
            <button
              onClick={handleBackToCalendar}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              ← Back to Calendar
            </button>
          )}
          
          {/* View Toggle Buttons */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setView('calendar')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                view === 'calendar'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              📅 Calendar
            </button>
            <button
              onClick={() => setView('list')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                view === 'list'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              📋 List
            </button>
            <button
              onClick={handleCheckInOut}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                view === 'checkin'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              🏨 Check-In/Out
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 px-4 py-3 rounded-md">
          {error}
          <button
            onClick={fetchBookings}
            className="ml-4 underline hover:no-underline"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Content */}
      {view === 'calendar' && (
        <>
          {/* Booking Statistics */}
          <BookingStats 
            bookings={bookings} 
            dateRange={dateRange}
          />

          {/* Calendar View */}
          <BookingCalendar
            bookings={bookings}
            isLoading={isLoading}
            dateRange={dateRange}
            onDateRangeChange={handleDateRangeChange}
            onBookingSelect={handleBookingSelect}
            onRefresh={fetchBookings}
          />
        </>
      )}

      {view === 'list' && (
        <>
          {/* Booking Statistics */}
          <BookingStats 
            bookings={bookings} 
            dateRange={dateRange}
          />

          {/* Filters */}
          <BookingFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            onRefresh={fetchBookings}
          />

          {/* Booking List */}
          <BookingList
            bookings={bookings}
            isLoading={isLoading}
            pagination={pagination}
            onBookingSelect={handleBookingSelect}
            onBookingUpdate={handleBookingUpdate}
            onBookingDelete={handleBookingDelete}
            onPageChange={handlePageChange}
            onRefresh={fetchBookings}
          />
        </>
      )}

      {view === 'details' && selectedBooking && (
        <BookingDetails
          booking={selectedBooking}
          isLoading={isLoading}
          onBookingUpdate={handleBookingUpdate}
          onBookingDelete={handleBookingDelete}
          onBack={handleBackToCalendar}
        />
      )}

      {view === 'checkin' && (
        <CheckInOutManager
          bookings={bookings}
          isLoading={isLoading}
          onBookingUpdate={handleBookingUpdate}
          onRefresh={fetchBookings}
        />
      )}
    </div>
  );
}
