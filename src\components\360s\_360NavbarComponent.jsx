'use client'
import Link from 'next/link';
import { useSearchParams } from 'next/navigation'
import ImageWrapperResponsive from '../ImageWrapperResponsive';
import _360BookNowBtn from './_360BookNowBtn';
import _360BtnMenu from './_360BtnMenu';
import { useContextExperience } from '@/contexts/useContextExperience';

export default function _360NavbarComponent() {
  const searchParams = useSearchParams()
  const { experienceState } = useContextExperience()
  const id = searchParams.get('id')

  // console.log('_360NavbarComponent:',id)
  return (
    (id !== 'entrance_360' && <nav className="flex fixed translate-y-1/2 z-20 top-0 left-0 w-full h-fit items-start from-black justify-between bg-gradient-to-b text-white">
      <div className='flex w-full items-center justify-between h-full'>
        <Link href={'/'} className="flex bg-inherit object-left-top relative w-fit h-full text-lg tracking-[6px]">
          <ImageWrapperResponsive className={'w-auto h-full'} src={'/assets/elephant_island_logo_white_for_nav_bar.png'} alt='elephant island logo'/>
        </Link>

        <div className='flex items-center w-fit h-full'>
          <_360BtnMenu/>
          <_360BookNowBtn/>
        </div>
      </div>
    </nav>)
  )
}
