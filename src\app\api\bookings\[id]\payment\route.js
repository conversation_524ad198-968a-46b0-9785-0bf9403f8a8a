import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// PUT /api/bookings/[id]/payment - Update payment status
export async function PUT(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const { paymentIntentId, status } = body;
    
    if (!paymentIntentId || !status) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'paymentIntentId and status are required',
        },
        { status: 400 }
      );
    }
    
    // Find the booking
    const booking = await Booking.findById(id)
      .populate('customer', 'firstname surname name email')
      .populate('package', 'name');
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    // Verify payment intent with Strip<PERSON>
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      if (paymentIntent.metadata.bookingId !== booking._id.toString()) {
        return NextResponse.json(
          {
            success: false,
            error: 'Payment Mismatch',
            message: 'Payment intent does not match this booking',
          },
          { status: 400 }
        );
      }
      
      // Update booking payment status
      booking.payment.status = status;
      booking.payment.stripePaymentIntentId = paymentIntentId;
      booking.payment.paidAt = new Date();
      booking.payment.amount = paymentIntent.amount / 100; // Convert from cents
      booking.payment.currency = paymentIntent.currency;
      
      // If payment is successful, update booking status
      if (status === 'paid') {
        booking.status = 'confirmed';
        booking.confirmedAt = new Date();
        
        // Add payment communication record
        await booking.addCommunication({
          type: 'payment',
          direction: 'inbound',
          subject: 'Payment Completed',
          content: `Payment of $${booking.payment.amount} completed via Stripe. Payment Intent: ${paymentIntentId}`,
          timestamp: new Date(),
        });
      }
      
      await booking.save();
      
      return NextResponse.json({
        success: true,
        data: {
          bookingId: booking._id,
          bookingNumber: booking.bookingNumber,
          paymentStatus: booking.payment.status,
          bookingStatus: booking.status,
          paidAmount: booking.payment.amount,
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount / 100,
            currency: paymentIntent.currency,
          },
        },
        message: 'Payment status updated successfully',
      });
    } catch (stripeError) {
      console.error('Stripe error:', stripeError);
      return NextResponse.json(
        {
          success: false,
          error: 'Stripe Error',
          message: 'Failed to verify payment with Stripe',
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating payment status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update payment status',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// GET /api/bookings/[id]/payment - Get payment status
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    
    const booking = await Booking.findById(id)
      .populate('customer', 'firstname surname name email')
      .populate('package', 'name');
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    let stripePaymentIntent = null;
    
    // If there's a Stripe payment intent, fetch its current status
    if (booking.payment.stripePaymentIntentId) {
      try {
        stripePaymentIntent = await stripe.paymentIntents.retrieve(
          booking.payment.stripePaymentIntentId
        );
      } catch (stripeError) {
        console.error('Error fetching Stripe payment intent:', stripeError);
      }
    }
    
    return NextResponse.json({
      success: true,
      data: {
        bookingId: booking._id,
        bookingNumber: booking.bookingNumber,
        bookingStatus: booking.status,
        payment: {
          status: booking.payment.status,
          amount: booking.payment.amount,
          currency: booking.payment.currency,
          paidAt: booking.payment.paidAt,
          stripePaymentIntentId: booking.payment.stripePaymentIntentId,
        },
        stripePaymentIntent: stripePaymentIntent ? {
          id: stripePaymentIntent.id,
          status: stripePaymentIntent.status,
          amount: stripePaymentIntent.amount / 100,
          currency: stripePaymentIntent.currency,
          created: new Date(stripePaymentIntent.created * 1000),
        } : null,
        totalAmount: booking.pricing.totalAmount,
      },
    });
  } catch (error) {
    console.error('Error getting payment status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get payment status',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
