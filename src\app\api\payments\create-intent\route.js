import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { bookingId, amount } = body;
    
    if (!bookingId || !amount) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'bookingId and amount are required',
        },
        { status: 400 }
      );
    }
    
    // Fetch booking to verify details
    const booking = await Booking.findById(bookingId)
      .populate('customer', 'firstname surname name email')
      .populate('package', 'name');
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    // Check if booking is still pending
    if (booking.status !== 'pending') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Status',
          message: 'Booking is not in pending status',
        },
        { status: 400 }
      );
    }
    
    // Check if payment is already completed
    if (['paid', 'completed'].includes(booking.payment.status)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Already Paid',
          message: 'Payment has already been completed for this booking',
        },
        { status: 400 }
      );
    }
    
    // Verify amount matches booking total
    if (amount !== booking.pricing.totalAmount) {
      return NextResponse.json(
        {
          success: false,
          error: 'Amount Mismatch',
          message: 'Payment amount does not match booking total',
        },
        { status: 400 }
      );
    }
    
    const customerName = booking.customer.firstname && booking.customer.surname 
      ? `${booking.customer.firstname} ${booking.customer.surname}`
      : booking.customer.name || 'Guest';
    
    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: 'usd',
      metadata: {
        bookingId: booking._id.toString(),
        bookingNumber: booking.bookingNumber,
        customerEmail: booking.customer.email,
        packageName: booking.package.name,
      },
      description: `Elephant Island Lodge - ${booking.package.name} - Booking #${booking.bookingNumber}`,
      receipt_email: booking.customer.email,
      shipping: {
        name: customerName,
        address: {
          line1: 'Elephant Island Lodge',
          city: 'Paradise Island',
          country: 'US',
        },
      },
    });
    
    // Update booking with payment intent ID
    booking.payment.stripePaymentIntentId = paymentIntent.id;
    booking.payment.status = 'processing';
    await booking.save();
    
    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    
    if (error.type === 'StripeCardError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Card Error',
          message: error.message,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Payment Error',
        message: 'Failed to create payment intent',
      },
      { status: 500 }
    );
  }
}
