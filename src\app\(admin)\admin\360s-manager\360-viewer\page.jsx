import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import ThreeSixtyViewer from '@/components/360s/360ViewerDashboard';

export default async function ThreeSixtyViewerPage() {
  const session = await auth();

  // Check if user is authenticated and has manager/admin role
  if (!session?.user || !['manager', 'admin'].includes(session.user.role)) {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-black">
      <ThreeSixtyViewer />
    </div>
  );
}

export const metadata = {
  title: '360° Panoramic Viewer - Admin Dashboard',
  description: 'View and manage 360° panoramic images for Elephant Island Lodge',
};
