'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import VideoStream from './VideoStream';

export default function HeroVideoClient({ videoPath, videoData }) {
  const router = useRouter();
  const [hasError, setHasError] = useState(false);

  // Handle case when videoPath is undefined or null
  useEffect(() => {
    if (!videoPath) {
      // If no video path is provided, redirect to 360s after a short delay
      const timer = setTimeout(() => {
        router.push('/360s?id=entrance_360');
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [videoPath, router]);

  // Handle video error
  const handleVideoError = () => {
    setHasError(true);
    // Redirect to 360s after a short delay
    setTimeout(() => {
      router.push('/360s?id=entrance_360');
    }, 1000);
  };

  return (
    <div className="absolute inset-0 w-full h-full">
      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black z-10">
          <div className="text-white text-center px-4">
            <p className="text-lg mb-2">Video could not be loaded.</p>
            <p className="text-sm opacity-75">Redirecting to 360° view...</p>
          </div>
        </div>
      )}

      {/* Video player */}
      {videoPath && (
        <VideoStream
          src={videoPath}
          autoPlay={true}
          loop={false}
          muted={true}
          controls={false}
          className="w-full h-full object-cover"
          preload="auto"
          playsInline={true}
          redirectTo="/360s?id=entrance_360"
          onError={handleVideoError}
        />
      )}

      {/* Fallback for no video */}
      {!videoPath && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black">
          <div className="text-white text-center px-4">
            <p className="text-lg mb-2">Loading video...</p>
            <p className="text-sm opacity-75">Redirecting to 360° view...</p>
          </div>
        </div>
      )}
    </div>
  );
}
