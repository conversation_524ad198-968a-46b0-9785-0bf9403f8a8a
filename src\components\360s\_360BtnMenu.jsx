'use client'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRef } from 'react'

export default function _360BtnMenu() {
  const {experienceState,disptachExperience} = useContextExperience()
  const menuRef = useRef(null)

  const handleClick = () => {
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
  }

  console.log('_360BtnMenu:',experienceState)

  return (
    <div
      ref={menuRef}
      onClick={handleClick}
      className="flex cursor-pointer w-24 h-full relative group"
    >
      {/* Hamburger menu (default state) */}
      <div className="flex flex-col gap-2 items-center justify-center w-full h-full group-hover:opacity-0 opacity-100 transition-opacity duration-200">
        <hr className="border-2 rounded-full w-10 border-white"/>
        <hr className="border-2 rounded-full w-10 border-white"/>
        <hr className="border-2 rounded-full w-10 border-white"/>
      </div>

      {/* X icon (hover state) */}
      <div className="flex absolute top-0 left-0 flex-col gap-2 items-center justify-center w-full h-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <hr className="absolute border-2 rotate-45 rounded-full w-10 border-white"/>
        <hr className="absolute border-2 -rotate-45 rounded-full w-10 border-white"/>
      </div>
    </div>
  )
}
