/**
 * Authentication Error Handler
 * 
 * Provides centralized error handling for authentication operations
 * with specific handling for rate limiting and other common auth errors
 */

export class AuthError extends Error {
  constructor(message, code, statusCode = 500) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
    this.statusCode = statusCode;
  }
}

export class RateLimitError extends AuthError {
  constructor(resetTime) {
    super('Too many requests. Please try again later.', 'RATE_LIMIT_EXCEEDED', 429);
    this.resetTime = resetTime;
  }
}

/**
 * Handle authentication errors with user-friendly messages
 */
export function handleAuthError(error) {
  console.error('Authentication error:', error);

  // Rate limiting errors
  if (error.message?.includes('429') || error.message?.includes('Too many requests')) {
    return {
      type: 'rate_limit',
      message: 'Too many requests. Please wait a moment and try again.',
      userMessage: 'Please wait a moment before trying again.',
      canRetry: true,
      retryAfter: 60000 // 1 minute
    };
  }

  // Network errors
  if (error.message?.includes('fetch') || error.message?.includes('network')) {
    return {
      type: 'network',
      message: 'Network error occurred during authentication.',
      userMessage: 'Please check your internet connection and try again.',
      canRetry: true,
      retryAfter: 5000 // 5 seconds
    };
  }

  // Session errors
  if (error.message?.includes('session') || error.message?.includes('token')) {
    return {
      type: 'session',
      message: 'Session error occurred.',
      userMessage: 'Your session has expired. Please sign in again.',
      canRetry: false,
      requiresSignIn: true
    };
  }

  // Database errors
  if (error.message?.includes('database') || error.message?.includes('connection')) {
    return {
      type: 'database',
      message: 'Database connection error.',
      userMessage: 'Service temporarily unavailable. Please try again later.',
      canRetry: true,
      retryAfter: 30000 // 30 seconds
    };
  }

  // Generic authentication errors
  return {
    type: 'auth',
    message: error.message || 'Authentication failed.',
    userMessage: 'Authentication failed. Please try again.',
    canRetry: true,
    retryAfter: 5000 // 5 seconds
  };
}

/**
 * Retry mechanism for authentication operations
 */
export async function retryAuthOperation(operation, maxRetries = 3, baseDelay = 1000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      const errorInfo = handleAuthError(error);
      
      // Don't retry if it's not retryable
      if (!errorInfo.canRetry) {
        throw error;
      }
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`Auth operation failed (attempt ${attempt}/${maxRetries}). Retrying in ${delay}ms...`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Show user-friendly error messages
 */
export function showAuthErrorMessage(error, options = {}) {
  const errorInfo = handleAuthError(error);
  
  if (options.useAlert !== false) {
    alert(errorInfo.userMessage);
  }
  
  if (options.onError) {
    options.onError(errorInfo);
  }
  
  return errorInfo;
}

/**
 * Enhanced sign-out function with error handling and retry
 */
export async function enhancedSignOut(signOutFunction, options = {}) {
  const {
    maxRetries = 2,
    showErrors = true,
    onError,
    onSuccess
  } = options;
  
  try {
    await retryAuthOperation(async () => {
      return await signOutFunction({
        callbackUrl: '/auth/signin',
        redirect: true
      });
    }, maxRetries);
    
    if (onSuccess) {
      onSuccess();
    }
    
  } catch (error) {
    const errorInfo = showAuthErrorMessage(error, { 
      useAlert: showErrors,
      onError 
    });
    
    // If it's a session error, force redirect to sign-in
    if (errorInfo.requiresSignIn) {
      window.location.href = '/auth/signin';
    }
    
    throw error;
  }
}

/**
 * Debounce function to prevent rapid successive calls
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function to limit function calls
 */
export function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Check if the current environment has rate limiting issues
 */
export function checkRateLimitStatus() {
  const rateLimitHeaders = {
    limit: document.querySelector('meta[name="x-ratelimit-limit"]')?.content,
    remaining: document.querySelector('meta[name="x-ratelimit-remaining"]')?.content,
    reset: document.querySelector('meta[name="x-ratelimit-reset"]')?.content
  };
  
  return {
    hasRateLimit: rateLimitHeaders.limit && rateLimitHeaders.limit !== 'unlimited',
    remaining: parseInt(rateLimitHeaders.remaining) || 999,
    resetTime: parseInt(rateLimitHeaders.reset) || Date.now() + 900000, // 15 minutes default
    isNearLimit: parseInt(rateLimitHeaders.remaining) < 10
  };
}

export default {
  AuthError,
  RateLimitError,
  handleAuthError,
  retryAuthOperation,
  showAuthErrorMessage,
  enhancedSignOut,
  debounce,
  throttle,
  checkRateLimitStatus
};
