import React, { useState } from 'react'
import Image from 'next/image'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRouter } from 'next/navigation'
import { settings } from '@/lib/settings'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

function BtnLandingpageComponent({data,fn,fnEntrance,fnHome,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',data?.naem,index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            onClick={data?.name=='home' ? fnHome : data?.name=='entrance' ? fnEntrance : ()=>fn(data?.name,data?.location)}
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function MenuPopup() {
  const lineClass2='w-full border-1 border-gray-400/30 '
  const lineClass='w-full border-1 mt-2 border-gray-400/30'
  const {disptachExperience}=useContextExperience()
  const router=useRouter()

  const handle360=(name,location)=>{
    // console.log('handle360:',name,location)
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
    router.push(`/360s?id=${location}_${name}`)
  }

  const handleEntrance=()=>{
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
    router.push(`/360s?id=entrance_360`)
  }

  const handleHome=()=>{
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
    router.push(`/`)
  }

  return (
    <div className='flex flex-col mt-10 w-40 h-fit'>
      <hr className={lineClass2}/>
      <div className='flex w-full h-fit'>
        {settings.menuPopup.home.map((i,index)=>
          <BtnLandingpageComponent key={index} index={index} fnEntrance={handleEntrance} fnHome={handleHome} data={i}/>
        )}
      </div>
      <hr className={lineClass2}/>
      {settings.menuPopup.entrance.map((i,index)=>
        <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
      )}
      <hr className={lineClass}/>
      {settings.menuPopup.firstFloor.map((i,index)=>
        <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
      )}
      <hr className={lineClass}/>
      {settings.menuPopup.outDoors.map((i,index)=>
        <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
      )}
      <hr className={lineClass}/>
      {settings.menuPopup.campOutskirts.map((i,index)=>
        <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
      )}
      <hr className={lineClass}/>
    </div>
  )
}
