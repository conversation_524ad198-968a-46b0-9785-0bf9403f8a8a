import React from 'react';
import Link from 'next/link';
import HeroVideoClient from './HeroVideoClient';
import { settings } from '@/lib/settings';

// Metadata for the page
export const metadata = {
  title: 'Hero Video',
  description: 'Full screen video',
};

export default async function page() {
  let videoData = null;

  try {
    // Use absolute URL for development to avoid SSL issues
    const apiUrl = process.env.NODE_ENV === 'development'
      ? 'http://localhost:3001/api/hero-videos/active'
      : `${settings.url}/api/hero-videos/active`;

    // Fetch active hero video data with error handling
    const response = await fetch(apiUrl, {
      next: { revalidate: 60 }, // Cache for 60 seconds
      // Add headers for development
      ...(process.env.NODE_ENV === 'development' && {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      })
    });

    if (response.ok) {
      const result = await response.json();
      if (result.success) {
        videoData = result.data;
      }
    } else {
      console.log('Hero video API response not ok:', response.status, response.statusText);
    }
  } catch (error) {
    // Enhanced error logging for development
    console.log('Error fetching hero video:', error.message);
    if (process.env.NODE_ENV === 'development') {
      console.error('Full error details:', error);
    }
  }

  return (
    <div className='relative w-full h-screen overflow-hidden bg-black'>
      {/* Skip button - only UI element as requested */}
      <Link
        href='/360s?id=entrance_360'
        className='fixed top-4 left-1/2 transform -translate-x-1/2 z-50 flex items-center justify-center h-10 px-6 bg-black/50 hover:bg-black/70 text-white border border-white/30 rounded-full transition-all duration-300 text-sm font-medium backdrop-blur-sm'
      >
        Skip
      </Link>

      {/* Hero video component */}
      <HeroVideoClient videoPath={videoData?.url} videoData={videoData} />
    </div>
  );
}
