import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { HeroVideo } from '@/models/HeroVideo';

// GET /api/hero-videos/active - Get the currently active hero video (public access)
export async function GET(request) {
  try {
    await connectDB();
    
    // Find the active hero video
    const activeVideo = await HeroVideo.findOne({ isActive: true });
    
    // If no active video found, get the most recently created one
    let videoToReturn = activeVideo;
    if (!activeVideo) {
      videoToReturn = await HeroVideo.findOne().sort({ createdAt: -1 });
    }
    
    if (!videoToReturn) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'No hero videos available',
        },
        { status: 404 }
      );
    }
    
    // Return only necessary fields for public consumption
    const publicVideoData = {
      _id: videoToReturn._id,
      name: videoToReturn.name,
      url: videoToReturn.url,
      contentType: videoToReturn.contentType,
      isActive: videoToReturn.isActive,
    };
    
    return NextResponse.json({
      success: true,
      data: publicVideoData,
      message: 'Active hero video retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching active hero video:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch active hero video',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
